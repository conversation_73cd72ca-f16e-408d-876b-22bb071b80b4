import requests
import os
import geopandas as gpd
from tqdm import tqdm
import json
import urllib3

# 🔕 Suppress SSL warnings (since we're ignoring certificate verification)
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Base REST endpoint
BASE_URL = "https://gis-pre.water.ie/server/rest/services/Web/WaterDistributionNetwork/MapServer"

# Output directory
OUTPUT_DIR = "downloaded_layers"
os.makedirs(OUTPUT_DIR, exist_ok=True)

def get_layer_ids(base_url):
    """Get all layer IDs from MapServer"""
    resp = requests.get(f"{base_url}?f=json", verify=False)
    if resp.status_code != 200:
        raise Exception("Failed to retrieve MapServer metadata.")
    data = resp.json()
    return [layer["id"] for layer in data.get("layers", [])]

def download_all_features(base_url, layer_id):
    """Download all features from a layer using pagination with auto-detected record limit"""
    layer_url = f"{base_url}/{layer_id}"
    metadata_resp = requests.get(f"{layer_url}?f=json", verify=False)
    if metadata_resp.status_code != 200:
        print(f"❌ Failed to fetch metadata for layer {layer_id}")
        return

    metadata = metadata_resp.json()
    layer_name = metadata.get("name", f"layer_{layer_id}").replace(" ", "_")
    object_id_field = metadata.get("objectIdField", "OBJECTID")
    max_records = metadata.get("maxRecordCount", 1000)  # Default fallback

    print(f"\n🔍 Layer: {layer_name} | ID: {layer_id} | Max Records per Query: {max_records}")

    features = []
    offset = 0

    while True:
        params = {
            "where": "1=1",
            "outFields": "*",
            "f": "geojson",
            "returnGeometry": "true",
            "resultOffset": offset,
            "resultRecordCount": max_records
        }

        query_url = f"{layer_url}/query"
        response = requests.get(query_url, params=params, verify=False)
        if response.status_code != 200:
            print(f"⚠️ Failed at offset {offset} for layer {layer_name}")
            break

        batch = response.json()
        new_features = batch.get("features", [])
        if not new_features:
            break

        features.extend(new_features)
        offset += max_records

        print(f"  ➕ Retrieved {len(new_features)} features (Total: {len(features)})")

    if not features:
        print(f"⚠️ No features found in layer {layer_name}")
        return

    # Save as GeoJSON
    geojson_data = {
        "type": "FeatureCollection",
        "features": features
    }

    output_path = os.path.join(OUTPUT_DIR, f"{layer_name}.geojson")
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(geojson_data, f)

    # Validate as GeoDataFrame
    try:
        gdf = gpd.GeoDataFrame.from_features(features)
        print(f"✅ Saved {len(gdf)} features to {output_path}")
    except Exception as e:
        print(f"⚠️ Error loading GeoDataFrame for {layer_name}: {e}")

def main():
    try:
        layer_ids = get_layer_ids(BASE_URL)
        for lid in tqdm(layer_ids, desc="Processing layers"):
            download_all_features(BASE_URL, lid)
    except Exception as e:
        print(f"🚨 Script failed: {e}")

if __name__ == "__main__":
    main()
